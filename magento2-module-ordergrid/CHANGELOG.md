# Changelog
All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](http://semver.org/spec/v2.0.0.html).

## [2.0.0] - 2025-05-13

### Fixed
- Order grid page errors when pipe symbol (and possibly other special characters) appear in SKU [#15](https://github.com/markshust/magento2-module-ordergrid/issues/15)

## [1.1.1] - 2022-02-10

### Fixed
- Bug when filtering by order date [#13](https://github.com/markshust/magento2-module-ordergrid/issues/13)

## [1.1.0] - 2022-08-01

### Updated
- Update order_items field into export-friendly format. [#12](https://github.com/markshust/magento2-module-ordergrid/issues/12)

## [1.0.9] - 2021-11-02

### Fixed
- Order grid filter does not work anymore. [#8](https://github.com/markshust/magento2-module-ordergrid/issues/8)

## [1.0.8] - 2021-04-16

### Fixed
- Added strict types and few other minor updates to pass static analysis checks. [ecd1da99](https://github.com/markshust/magento2-module-ordergrid/commit/ecd1da99c9019c95f957166fb312368497271b9a)

## [1.0.7] - 2021-02-26

### Fixed
- Count always displays “”1 records found”. [#5](https://github.com/markshust/magento2-module-ordergrid/pull/5)
- Cannot select all items. [#5](https://github.com/markshust/magento2-module-ordergrid/pull/5)

## [1.0.6] - 2021-02-04

### Fixed
- Version bump for Composer package resolution.

## [1.0.5] - 2021-02-04

### Fixed
- Fixed filters and order grid styles [#4](https://github.com/markshust/magento2-module-ordergrid/pull/4).

## [1.0.4] - 2020-04-22

### Fixed
- Updated php and magento framework version constraints to >=7.1 and >=101 respectively, for more seamless compatibility with new Magento releases.

## [1.0.3] - 2020-04-22

### Fixed
- Updated php version constraint to ~7.1 (any version 7.1 or higher).

## [1.0.2] - 2019-10-18

### Fixed
- Updated composer version constraints to support newer versions of Magento.

## [1.0.1] - 2019-05-10

### Fixed
- Fixes suggested by Magento Coding Standard.

## [1.0.0] - 2019-01-11

### Added
- Initial release.
