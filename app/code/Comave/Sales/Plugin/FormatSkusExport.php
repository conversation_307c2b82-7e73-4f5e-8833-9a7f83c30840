<?php declare(strict_types=1);

namespace MarkShust\OrderGrid\Plugin;

use Psr\Log\LoggerInterface;
use Magento\Framework\Api\Search\DocumentInterface;
use Magento\Ui\Model\Export\MetadataProvider;

class FormatSkusExport
{
    private LoggerInterface $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public function beforeGetRowData(
        MetadataProvider   $subject,
        DocumentInterface $document,
        $fields,
        $options
    ): array {
        if ($sku = $document->getData('sku')) {
            $this->logger->debug('[FormatSkusExport] beforeGetRowData: raw sku', ['sku' => $sku]);
            $parts = array_map('trim', explode(',', $sku));
            $new   = implode('|', $parts);
            $document->setData('sku', $new);
            $this->logger->debug('[FormatSkusExport] beforeGetRowData: transformed sku', ['sku' => $new]);
        }
        return [$document, $fields, $options];
    }
}
