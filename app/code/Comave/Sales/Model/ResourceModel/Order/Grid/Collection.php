<?php declare(strict_types=1);

namespace MarkShust\OrderGrid\Model\ResourceModel\Order\Grid;

use Psr\Log\LoggerInterface;
use Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult;
use Magento\Sales\Model\ResourceModel\Order\Grid\Collection as OrderGridCollection;

class Collection extends OrderGridCollection
{
    private const SKU_FIELD       = 'sku';
    private const SKU_FILTER_FLAG = 'sku_filter_added';
    private const ITEM_TABLE      = 'sales_order_item';

    private LoggerInterface $logger;

    public function __construct(
        \Magento\Framework\Data\Collection\EntityFactoryInterface $entityFactory,
        \Psr\Log\LoggerInterface                         $logger,
        // … (other core constructor args) …
        $mainTable,
        $resourceModel,
        $connectionName = null
    ) {
        parent::__construct($entityFactory, /*…*/ $mainTable, $resourceModel, $connectionName);
        $this->logger = $logger;
    }

    public function addFieldToFilter($field, $condition = null): self
    {
        if ($field === self::SKU_FIELD && ! $this->getFlag(self::SKU_FILTER_FLAG)) {
            $this->logger->debug('[OrderGrid/Collection] addFieldToFilter: sku path reached', ['condition' => $condition]);
            return $this->addSkuFilter($condition);
        }
        return parent::addFieldToFilter($field, $condition);
    }

    private function addSkuFilter(array|int|string|null $condition): self
    {
        $this->logger->debug('[OrderGrid/Collection] addSkuFilter: joining sales_order_item');
        $alias = 'soi';
        $this->getSelect()
            ->join([$alias => $this->getTable(self::ITEM_TABLE)], "main_table.entity_id = {$alias}.order_id", [])
            ->group('main_table.entity_id');

        $this->addFieldToFilter("{$alias}.sku", $condition);
        $this->setFlag(self::SKU_FILTER_FLAG, true);
        return $this;
    }

    protected function _afterLoad(): SearchResult
    {
        $this->logger->debug('[OrderGrid/Collection] _afterLoad called; injecting SKUs');
        $orderIds = $this->getColumnValues('entity_id');
        if ($orderIds) {
            $this->injectSkus($orderIds);
        }
        return parent::_afterLoad();
    }

    private function injectSkus(array $orderIds): void
    {
        $this->logger->debug('[OrderGrid/Collection] injectSkus: fetching SKUs', ['order_ids' => $orderIds]);
        $rows    = $this->fetchOrderSkus($orderIds);
        $byOrder = [];
        foreach ($rows as $r) {
            $byOrder[(int)$r['order_id']][] = $r['sku'];
        }

        foreach ($orderIds as $id) {
            $item = $this->getItemById((int)$id);
            if ($item && ! empty($byOrder[$id])) {
                $list = implode(', ', array_unique($byOrder[$id]));
                $item->setData(self::SKU_FIELD, $list);
            }
        }
    }

    private function fetchOrderSkus(array $orderIds): array
    {
        $this->logger->debug('[OrderGrid/Collection] fetchOrderSkus', ['order_ids' => $orderIds]);
        return $this->getConnection()
            ->select()
            ->from($this->getTable(self::ITEM_TABLE), ['order_id', 'sku'])
            ->where('order_id IN (?)', $orderIds)
            ->where('parent_item_id IS NULL')
            ->query()
            ->fetchAll();
    }
}
