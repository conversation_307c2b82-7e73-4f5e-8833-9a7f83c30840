<?php
declare(strict_types=1);

namespace Comave\Marketplace\Plugin;

use Magento\Customer\Controller\Account\LogoutSuccess;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\App\RequestInterface;

class LogoutSuccessPlugin
{
    private readonly RedirectFactory $resultRedirectFactory;
    private readonly RequestInterface $request;

    public function __construct(
        RedirectFactory $resultRedirectFactory,
        RequestInterface $request
    ) {
        $this->resultRedirectFactory = $resultRedirectFactory;
        $this->request = $request;
    }

    /**
     * Execute plugin for LogoutSuccess controller
     *
     * @param \Magento\Customer\Controller\Account\LogoutSuccess $subject
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function aroundExecute(LogoutSuccess $subject, \Closure $proceed): ResultInterface
    {
        $referer = $this->request->getServer('HTTP_REFERER');

        if ($referer && strpos($referer, '/marketplace/') !== false) {
            $resultRedirect = $this->resultRedirectFactory->create();
            $resultRedirect->setPath('marketplace/account/login');
            return $resultRedirect;
        }

        return $proceed();
    }
}
