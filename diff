diff --git a/app/code/Comave/Sales/Model/EanProvider.php b/app/code/Comave/Sales/Model/EanProvider.php
new file mode 100644
index 000000000..4913635ae
--- /dev/null
+++ b/app/code/Comave/Sales/Model/EanProvider.php
@@ -0,0 +1,56 @@
+<?php
+declare(strict_types=1);
+
+namespace Comave\Sales\Model;
+
+use Magento\Framework\App\ResourceConnection;
+use Magento\Sales\Model\ResourceModel\Order\Grid\Collection;
+
+class EanProvider
+{
+    public function __construct(
+        private readonly ResourceConnection $resourceConnection
+    ) {}
+
+    /**
+     * @param Collection|null $collection
+     *   If passed, we use its connection; otherwise fallback to default.
+     */
+    public function getEanAttributeId(?Collection $collection = null): ?int
+    {
+        $connection = $collection
+            ? $collection->getConnection()
+            : $this->resourceConnection->getConnection();
+
+        try {
+            $select = $connection->select()
+                ->from(
+                    ['ea' => $connection->getTableName('eav_attribute')],
+                    ['attribute_id']
+                )
+                ->where('ea.entity_type_id = ?', 4)
+                ->where('ea.attribute_code = ?', 'ean');
+
+            $attributeId = $connection->fetchOne($select);
+            return $attributeId ? (int)$attributeId : null;
+        } catch (\Exception $e) {
+            return null;
+        }
+    }
+
+    public function hasRowIdColumn(?Collection $collection = null): bool
+    {
+        $connection = $collection
+            ? $collection->getConnection()
+            : $this->resourceConnection->getConnection();
+
+        try {
+            $columns = $connection->describeTable(
+                $connection->getTableName('catalog_product_entity')
+            );
+            return isset($columns['row_id']);
+        } catch (\Exception $e) {
+            return false;
+        }
+    }
+}
diff --git a/app/code/Comave/Sales/Plugin/OrderGridSkuEanSearchPlugin.php b/app/code/Comave/Sales/Plugin/OrderGridSkuEanSearchPlugin.php
new file mode 100644
index 000000000..ca73e66e5
--- /dev/null
+++ b/app/code/Comave/Sales/Plugin/OrderGridSkuEanSearchPlugin.php
@@ -0,0 +1,93 @@
+<?php
+declare(strict_types=1);
+
+namespace Comave\Sales\Plugin;
+
+use Magento\Sales\Model\ResourceModel\Order\Grid\Collection;
+use Comave\Sales\Model\EanProvider;
+
+class OrderGridSkuEanSearchPlugin
+{
+    public function __construct(
+        private readonly EanProvider $eanProvider
+    ) {}
+
+    /**
+     * Intercept addFieldToFilter to enable SKU/EAN searching.
+     *
+     * @param Collection $collection
+     * @param \Closure $proceed
+     * @param string $field
+     * @param mixed $condition
+     * @return Collection
+     */
+    public function aroundAddFieldToFilter(
+        Collection $collection,
+        \Closure $proceed,
+        $field,
+        $condition = null
+    ) {
+        if ($field !== 'sku_ean') {
+            return $proceed($field, $condition);
+        }
+
+        if (!$collection->getFlag('sku_ean_filter_added')) {
+            $collection->setFlag('sku_ean_filter_added', true);
+            $this->addSkuEanJoinsForFilter($collection);
+        }
+
+        $searchValue = is_array($condition) && isset($condition['like']) 
+            ? trim($condition['like'], '%') 
+            : (string)$condition;
+
+        $eanAttrId = $this->eanProvider->getEanAttributeId($collection);
+        
+        $orConditions = [];
+        $orConditions[] = $collection->getConnection()->quoteInto('soi.sku = ?', $searchValue);
+        
+        if ($eanAttrId) {
+            $orConditions[] = $collection->getConnection()->quoteInto('ean_attr.value = ?', $searchValue);
+        }
+        
+        $collection->getSelect()->where('(' . implode(' OR ', $orConditions) . ')');
+
+        return $collection;
+    }
+
+    /**
+     * Add SKU/EAN joins for filtering only.
+     *
+     * @param Collection $collection
+     * @return void
+     */
+    private function addSkuEanJoinsForFilter(Collection $collection): void
+    {
+        $collection->getSelect()->joinLeft(
+            ['soi' => $collection->getTable('sales_order_item')],
+            'main_table.entity_id = soi.order_id',
+            []
+        );
+
+        $collection->getSelect()->joinLeft(
+            ['cpe' => $collection->getTable('catalog_product_entity')],
+            'soi.product_id = cpe.entity_id',
+            []
+        );
+
+        $eanAttrId = $this->eanProvider->getEanAttributeId($collection);
+        if ($eanAttrId) {
+            $joinCondition = $this->eanProvider->hasRowIdColumn($collection)
+                ? 'cpe.row_id = ean_attr.row_id'
+                : 'cpe.entity_id = ean_attr.entity_id';
+            $joinCondition .= ' AND ean_attr.attribute_id = ' . $eanAttrId . ' AND ean_attr.store_id = 0';
+
+            $collection->getSelect()->joinLeft(
+                ['ean_attr' => $collection->getTable('catalog_product_entity_varchar')],
+                $joinCondition,
+                []
+            );
+        }
+
+        $collection->getSelect()->group('main_table.entity_id');
+    }
+}
diff --git a/app/code/Comave/Sales/Ui/Component/Listing/Column/SkuEan.php b/app/code/Comave/Sales/Ui/Component/Listing/Column/SkuEan.php
new file mode 100644
index 000000000..f6b85f339
--- /dev/null
+++ b/app/code/Comave/Sales/Ui/Component/Listing/Column/SkuEan.php
@@ -0,0 +1,143 @@
+<?php
+declare(strict_types=1);
+
+namespace Comave\Sales\Ui\Component\Listing\Column;
+
+use Magento\Framework\View\Element\UiComponentFactory;
+use Magento\Framework\View\Element\UiComponent\ContextInterface;
+use Magento\Ui\Component\Listing\Columns\Column;
+use Magento\Framework\App\ResourceConnection;
+use Comave\Sales\Model\EanProvider;
+
+
+class SkuEan extends Column
+{
+    /**
+     * @param ContextInterface $context
+     * @param UiComponentFactory $uiComponentFactory
+     * @param ResourceConnection $resourceConnection
+     * @param EanProvider $eanProvider
+     * @param array $components
+     * @param array $data
+     */
+    public function __construct(
+        ContextInterface   $context,
+        UiComponentFactory $uiComponentFactory,
+        private readonly   ResourceConnection $resourceConnection,
+        private readonly   EanProvider        $eanProvider,
+        array $components = [],
+        array $data = []
+    ) {
+        parent::__construct($context, $uiComponentFactory, $components, $data);
+    }
+
+    /**
+     * Prepare data source for SKU/EAN column.
+     *
+     * @param array $dataSource
+     * @return array
+     */
+    public function prepareDataSource(array $dataSource)
+    {
+        if (isset($dataSource['data']['items'])) {
+            $fieldName = $this->getData('name');
+            foreach ($dataSource['data']['items'] as &$item) {
+                $item[$fieldName] = $this->getSkuEanForOrder((int)$item['entity_id']);
+            }
+        }
+        return $dataSource;
+    }
+
+    /**
+     * Get SKU and EAN values for an order.
+     *
+     * @param int $orderId
+     * @return string
+     */
+    private function getSkuEanForOrder(int $orderId): string
+    {
+        $connection = $this->resourceConnection->getConnection();
+        $select = $connection->select()
+            ->from(['soi' => $this->resourceConnection->getTableName('sales_order_item')], ['sku'])
+            ->where('soi.order_id = ?', $orderId)
+            ->where('soi.parent_item_id IS NULL');
+
+        $skus = $connection->fetchCol($select);
+
+        if (empty($skus)) {
+            return '';
+        }
+
+        $eanValues = $this->getEanValues($skus);
+        $result = [];
+        foreach ($skus as $sku) {
+            $eanValue = isset($eanValues[$sku]) && !empty($eanValues[$sku]) ? $eanValues[$sku] : '';
+            if (!empty($eanValue)) {
+                $result[] = $sku . ' / ' . $eanValue;
+            } else {
+                $result[] = $sku;
+            }
+        }
+
+        return implode('<br/>', $result);
+    }
+
+    /**
+     * Get EAN values for given SKUs.
+     *
+     * @param array $skus
+     * @return array
+     */
+    private function getEanValues(array $skus): array
+    {
+        if (empty($skus)) {
+            return [];
+        }
+
+        $connection = $this->resourceConnection->getConnection();
+
+        $eanAttrId = $this->eanProvider->getEanAttributeId();
+        if (!$eanAttrId) {
+            return [];
+        }
+
+        try {
+            $varcharColumns = $connection->describeTable(
+                $this->resourceConnection->getTableName('catalog_product_entity_varchar')
+            );
+
+            $varcharLinkField = isset($varcharColumns['row_id']) ? 'row_id' : 'entity_id';
+            $useRowId          = $this->eanProvider->hasRowIdColumn();
+            $mainLinkField = $useRowId ? 'row_id' : 'entity_id';
+
+            $select = $connection->select()
+                ->from(['cpe' => $this->resourceConnection->getTableName('catalog_product_entity')], [$mainLinkField, 'sku'])
+                ->where('cpe.sku IN (?)', $skus);
+
+            $productData = $connection->fetchPairs($select);
+            if (empty($productData)) {
+                return [];
+            }
+
+            $select = $connection->select()
+                ->from(['cpev' => $this->resourceConnection->getTableName('catalog_product_entity_varchar')], [$varcharLinkField, 'value'])
+                ->where('cpev.' . $varcharLinkField . ' IN (?)', array_keys($productData))
+                ->where('cpev.attribute_id = ?', $eanAttrId)
+                ->where('cpev.store_id = 0');
+
+            $eanData = $connection->fetchPairs($select);
+
+            $result = [];
+            foreach ($productData as $linkId => $sku) {
+                if (isset($eanData[$linkId])) {
+                    $result[$sku] = $eanData[$linkId];
+                }
+            }
+
+            return $result;
+
+        } catch (\Exception $e) {
+            return [];
+        }
+    }
+}
diff --git a/app/code/Comave/Sales/etc/di.xml b/app/code/Comave/Sales/etc/di.xml
index c88aa4038..149439ae9 100644
--- a/app/code/Comave/Sales/etc/di.xml
+++ b/app/code/Comave/Sales/etc/di.xml
@@ -136,4 +136,7 @@
         </arguments>
     </type>
 
+    <type name="Magento\Sales\Model\ResourceModel\Order\Grid\Collection">
+        <plugin name="comave_sales_order_grid_sku_ean_search" type="Comave\Sales\Plugin\OrderGridSkuEanSearchPlugin" />
+    </type>
 </config>
diff --git a/app/code/Comave/Sales/view/adminhtml/ui_component/sales_order_grid.xml b/app/code/Comave/Sales/view/adminhtml/ui_component/sales_order_grid.xml
new file mode 100644
index 000000000..cfb642aa4
--- /dev/null
+++ b/app/code/Comave/Sales/view/adminhtml/ui_component/sales_order_grid.xml
@@ -0,0 +1,14 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
+         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd"
+         name="sales_order_grid">
+    <columns name="sales_order_columns">
+        <column name="sku_ean" class="Comave\Sales\Ui\Component\Listing\Column\SkuEan" sortOrder="1000">
+            <settings>
+                <filter>text</filter>
+                <label translate="true">SKU/EAN</label>
+                <bodyTmpl>ui/grid/cells/html</bodyTmpl>
+            </settings>
+        </column>
+    </columns>
+</listing>
